//
//  LNMessagesListController.swift
//  LiveNow
//
//  Created by ji<PERSON><PERSON> on 2025/8/16.
//

import UIKit
import JKSwiftExtension
class LNMessagesListController: LNBaseController {
    override var prefersNavigationBarHide: Bool {
        return true
    }
    // "谁喜欢我"卡片视图
    private lazy var whoLikedMeCard: LNWhoLikedMeCardView = {
        let view = LNWhoLikedMeCardView()
        view.layer.cornerRadius = s(8)
        view.clipsToBounds = true
        view.layer.borderColor = UIColor.hex(hexString: "#72EECB").cgColor
        view.layer.borderWidth = 1
        return view
    }()
    
    // 消息表格视图
    private lazy var tableView: UITableView = {
        let table = UITableView()
        table.backgroundColor = .white
        table.separatorStyle = .none
        table.register(MessageCell.self, forCellReuseIdentifier: "MessageCell")
        table.delegate = self
        table.dataSource = self
        return table
    }()
    
    // 消息数据
    private var messages: [MessageModel] = []
    
    // MARK: - 生命周期
    override func viewDidLoad() {
        super.viewDidLoad()
        view.backgroundColor = .clear
        setupUI()
        loadMessages()
    }
    override func viewDidLayoutSubviews() {
        super.viewDidLayoutSubviews()
        tableView.jk.addCorner(conrners: [.topLeft,.topRight], radius: s(30))
    }
    // MARK: - 私有方法
    private func setupUI() {
        view.addSubview(whoLikedMeCard)
        view.addSubview(tableView)

        // 设置"谁喜欢我"卡片的点击回调
        whoLikedMeCard.onTapped = { [weak self] in
            self?.navigateToWhoLikedMe()
        }

        whoLikedMeCard.snp.makeConstraints { make in
            make.top.equalTo(view).offset(s(10))
            make.left.equalTo(view).offset(s(15))
            make.right.equalTo(view).offset(-s(15))
            make.height.equalTo(s(38))
        }
        tableView.snp.makeConstraints { make in
            make.top.equalTo(whoLikedMeCard.snp.bottom).offset(10)
            make.left.right.bottom.equalTo(view)
        }
    }

    /// 跳转到"谁喜欢我"页面
    private func navigateToWhoLikedMe() {
        let whoLikedMeVC = LNWhoLikedMeViewController()
        whoLikedMeVC.hidesBottomBarWhenPushed = true
        navigationController?.pushViewController(whoLikedMeVC, animated: true)
    }
    
    private func loadMessages() {
        // 创建模拟数据
        let mockMessages = [
            MessageModel(userName: "Usre name", content: "The Content Of The Message....", time: "PM 2:40"),
            MessageModel(userName: "Usre name", content: "The Content Of The Message....", time: "PM 2:40"),
            MessageModel(userName: "Usre name", content: "The Content Of The Message....", time: "PM 2:40"),
            MessageModel(userName: "Usre name", content: "The Content Of The Message....", time: "PM 2:40"),
            MessageModel(userName: "Usre name", content: "The Content Of The Message....", time: "PM 2:40")
        ]
        
        messages = mockMessages
        tableView.reloadData()
    }
}

// MARK: - UITableViewDelegate, UITableViewDataSource
extension LNMessagesListController: UITableViewDelegate, UITableViewDataSource {
    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        return messages.count
    }
    
    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        let cell = tableView.dequeueReusableCell(withIdentifier: "MessageCell", for: indexPath) as! MessageCell
        let message = messages[indexPath.row]
        cell.configure(with: message)
        return cell
    }
    
    func tableView(_ tableView: UITableView, heightForRowAt indexPath: IndexPath) -> CGFloat {
        return 80
    }
    func tableView(_ tableView: UITableView, didSelectRowAt indexPath: IndexPath) {
        navigationController?.pushViewController(LNSessionViewController(), animated: true)
    }
}
