//
//  LNVIPLevelCarouselIntegration.swift
//  LiveNow
//
//  Created by AI on 2025/8/24.
//

import UIKit
import SnapKit

/// VIP等级轮播集成示例 - 展示如何在实际页面中使用轮播组件
class LNVIPLevelCarouselIntegration: LNBaseController {
    
    // MARK: - Properties
    
    /// 当前用户VIP等级
    private var currentVIPLevel: Int = 0
    
    // MARK: - UI Elements
    
    /// 页面标题
    private lazy var titleLabel: UILabel = {
        let label = UILabel()
        label.text = "My VIP Level"
        label.font = LNFont.bold(24)
        label.textColor = UIColor(hexString: "#1F1F1F")
        label.textAlignment = .center
        return label
    }()
    
    /// VIP等级轮播视图
    private lazy var vipCarouselView: LNVIPLevelCarouselView = {
        let view = LNVIPLevelCarouselView()
        view.backgroundColor = .clear
        
        // 设置页面变化回调
        view.onPageChanged = { [weak self] level in
            self?.handleVIPLevelChanged(level)
        }
        
        // 设置点击回调
        view.onItemTapped = { [weak self] level in
            self?.handleVIPLevelTapped(level)
        }
        
        return view
    }()
    
    /// VIP等级信息容器
    private lazy var infoContainerView: UIView = {
        let view = UIView()
        view.backgroundColor = .white
        view.layer.cornerRadius = s(16)
        view.layer.shadowColor = UIColor.black.cgColor
        view.layer.shadowOffset = CGSize(width: 0, height: s(2))
        view.layer.shadowRadius = s(8)
        view.layer.shadowOpacity = 0.1
        return view
    }()
    
    /// 当前等级标签
    private lazy var currentLevelLabel: UILabel = {
        let label = UILabel()
        label.text = "Current Level: VIP 0"
        label.font = LNFont.bold(18)
        label.textColor = UIColor(hexString: "#1F1F1F")
        label.textAlignment = .center
        return label
    }()
    
    /// 等级描述标签
    private lazy var levelDescriptionLabel: UILabel = {
        let label = UILabel()
        label.text = "Free Level - Basic features available"
        label.font = LNFont.regular(14)
        label.textColor = UIColor(hexString: "#666666")
        label.textAlignment = .center
        label.numberOfLines = 0
        return label
    }()
    
    /// 升级按钮
    private lazy var upgradeButton: UIButton = {
        let button = UIButton(type: .custom)
        button.setTitle("Upgrade to VIP", for: .normal)
        button.setTitleColor(.white, for: .normal)
        button.titleLabel?.font = LNFont.bold(16)
        button.backgroundColor = UIColor.systemOrange
        button.layer.cornerRadius = s(12)
        button.addTarget(self, action: #selector(upgradeButtonTapped), for: .touchUpInside)
        return button
    }()
    
    /// 特权列表标签
    private lazy var privilegesLabel: UILabel = {
        let label = UILabel()
        label.text = "VIP Privileges:"
        label.font = LNFont.bold(16)
        label.textColor = UIColor(hexString: "#1F1F1F")
        label.textAlignment = .left
        return label
    }()
    
    /// 特权内容标签
    private lazy var privilegesContentLabel: UILabel = {
        let label = UILabel()
        label.text = "• Basic chat features\n• Standard video quality\n• Limited daily matches"
        label.font = LNFont.regular(14)
        label.textColor = UIColor(hexString: "#666666")
        label.textAlignment = .left
        label.numberOfLines = 0
        return label
    }()
    
    // MARK: - Lifecycle
    
    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        setupConstraints()
        loadUserVIPLevel()
    }
    
    // MARK: - Setup Methods
    
    private func setupUI() {
        title = "VIP Level"
        view.backgroundColor = UIColor(hexString: "#F5F5F5")
        
        view.addSubview(titleLabel)
        view.addSubview(vipCarouselView)
        view.addSubview(infoContainerView)
        
        infoContainerView.addSubview(currentLevelLabel)
        infoContainerView.addSubview(levelDescriptionLabel)
        infoContainerView.addSubview(upgradeButton)
        infoContainerView.addSubview(privilegesLabel)
        infoContainerView.addSubview(privilegesContentLabel)
    }
    
    private func setupConstraints() {
        // 标题约束
        titleLabel.snp.makeConstraints { make in
            make.top.equalTo(view.safeAreaLayoutGuide).offset(s(20))
            make.left.right.equalToSuperview().inset(s(20))
        }
        
        // 轮播视图约束
        vipCarouselView.snp.makeConstraints { make in
            make.top.equalTo(titleLabel.snp.bottom).offset(s(20))
            make.left.right.equalToSuperview().inset(s(20))
            make.height.equalTo(s(200))
        }
        
        // 信息容器约束
        infoContainerView.snp.makeConstraints { make in
            make.top.equalTo(vipCarouselView.snp.bottom).offset(s(20))
            make.left.right.equalToSuperview().inset(s(20))
            make.bottom.lessThanOrEqualTo(view.safeAreaLayoutGuide).offset(-s(20))
        }
        
        // 当前等级标签约束
        currentLevelLabel.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(s(20))
            make.left.right.equalToSuperview().inset(s(20))
        }
        
        // 等级描述约束
        levelDescriptionLabel.snp.makeConstraints { make in
            make.top.equalTo(currentLevelLabel.snp.bottom).offset(s(8))
            make.left.right.equalToSuperview().inset(s(20))
        }
        
        // 升级按钮约束
        upgradeButton.snp.makeConstraints { make in
            make.top.equalTo(levelDescriptionLabel.snp.bottom).offset(s(20))
            make.left.right.equalToSuperview().inset(s(20))
            make.height.equalTo(s(48))
        }
        
        // 特权标题约束
        privilegesLabel.snp.makeConstraints { make in
            make.top.equalTo(upgradeButton.snp.bottom).offset(s(20))
            make.left.right.equalToSuperview().inset(s(20))
        }
        
        // 特权内容约束
        privilegesContentLabel.snp.makeConstraints { make in
            make.top.equalTo(privilegesLabel.snp.bottom).offset(s(8))
            make.left.right.equalToSuperview().inset(s(20))
            make.bottom.equalToSuperview().offset(-s(20))
        }
    }
    
    // MARK: - Data Loading
    
    /// 加载用户VIP等级
    private func loadUserVIPLevel() {
        // 从用户管理器获取当前VIP等级
        if let user = LNUserManager.shared.userModel {
            currentVIPLevel = user.levelKey
        } else {
            currentVIPLevel = 0
        }
        
        // 设置轮播视图的当前等级
        vipCarouselView.setCurrentLevel(currentVIPLevel)
        
        // 更新界面信息
        updateVIPLevelInfo(currentVIPLevel)
    }
    
    // MARK: - Event Handlers
    
    /// 处理VIP等级变化
    private func handleVIPLevelChanged(_ level: Int) {
        updateVIPLevelInfo(level)
    }
    
    /// 处理VIP等级点击
    private func handleVIPLevelTapped(_ level: Int) {
        if level > currentVIPLevel {
            // 点击了更高等级，显示升级选项
            showUpgradeOptions(for: level)
        } else if level == currentVIPLevel {
            // 点击了当前等级，显示详细信息
            showCurrentLevelDetails()
        } else {
            // 点击了较低等级，显示历史信息
            showLevelHistory(for: level)
        }
    }
    
    /// 更新VIP等级信息显示
    private func updateVIPLevelInfo(_ level: Int) {
        currentLevelLabel.text = "Current Level: VIP \(level)"
        
        // 更新等级描述
        let descriptions = [
            "Free Level - Basic features available",
            "Bronze VIP - Enhanced chat features",
            "Silver VIP - Premium video quality",
            "Gold VIP - Unlimited daily matches",
            "Platinum VIP - Priority customer support",
            "Diamond VIP - Exclusive premium features",
            "Crown VIP - Ultimate VIP experience"
        ]
        
        levelDescriptionLabel.text = descriptions[safe: level] ?? "Unknown Level"
        
        // 更新特权列表
        updatePrivilegesList(for: level)
        
        // 更新升级按钮
        updateUpgradeButton(for: level)
    }
    
    /// 更新特权列表
    private func updatePrivilegesList(for level: Int) {
        let privileges = [
            "• Basic chat features\n• Standard video quality\n• Limited daily matches",
            "• Enhanced chat features\n• HD video quality\n• 50 daily matches",
            "• Premium chat features\n• Full HD video quality\n• 100 daily matches",
            "• Advanced chat features\n• 4K video quality\n• Unlimited daily matches",
            "• Priority chat features\n• Ultra HD video quality\n• Priority customer support",
            "• Exclusive chat features\n• Premium video effects\n• VIP customer support",
            "• Ultimate chat features\n• All premium features\n• Dedicated account manager"
        ]
        
        privilegesContentLabel.text = privileges[safe: level] ?? "No privileges available"
    }
    
    /// 更新升级按钮
    private func updateUpgradeButton(for level: Int) {
        if level > currentVIPLevel {
            upgradeButton.setTitle("Upgrade to VIP \(level)", for: .normal)
            upgradeButton.backgroundColor = UIColor.systemOrange
            upgradeButton.isEnabled = true
        } else if level == currentVIPLevel {
            upgradeButton.setTitle("Current Level", for: .normal)
            upgradeButton.backgroundColor = UIColor.systemGreen
            upgradeButton.isEnabled = false
        } else {
            upgradeButton.setTitle("Previous Level", for: .normal)
            upgradeButton.backgroundColor = UIColor.systemGray
            upgradeButton.isEnabled = false
        }
    }
    
    // MARK: - Actions
    
    @objc private func upgradeButtonTapped() {
        let currentDisplayLevel = vipCarouselView.getCurrentLevel()
        if currentDisplayLevel > currentVIPLevel {
            showUpgradeOptions(for: currentDisplayLevel)
        }
    }
    
    /// 显示升级选项
    private func showUpgradeOptions(for level: Int) {
        let alert = UIAlertController(
            title: "Upgrade to VIP \(level)",
            message: "Would you like to upgrade to VIP \(level)?",
            preferredStyle: .alert
        )
        
        alert.addAction(UIAlertAction(title: "Upgrade", style: .default) { _ in
            self.performUpgrade(to: level)
        })
        
        alert.addAction(UIAlertAction(title: "Cancel", style: .cancel))
        
        present(alert, animated: true)
    }
    
    /// 显示当前等级详情
    private func showCurrentLevelDetails() {
        let alert = UIAlertController(
            title: "VIP \(currentVIPLevel) Details",
            message: "You are currently at VIP \(currentVIPLevel). Enjoy your premium features!",
            preferredStyle: .alert
        )
        
        alert.addAction(UIAlertAction(title: "OK", style: .default))
        present(alert, animated: true)
    }
    
    /// 显示等级历史
    private func showLevelHistory(for level: Int) {
        let alert = UIAlertController(
            title: "VIP \(level) History",
            message: "This is a previous VIP level. You have already surpassed this level!",
            preferredStyle: .alert
        )
        
        alert.addAction(UIAlertAction(title: "OK", style: .default))
        present(alert, animated: true)
    }
    
    /// 执行升级
    private func performUpgrade(to level: Int) {
        // 这里应该调用实际的升级API
        // 目前只是模拟升级成功
        
        currentVIPLevel = level
        vipCarouselView.setCurrentLevel(level)
        updateVIPLevelInfo(level)
        
        let alert = UIAlertController(
            title: "Upgrade Successful!",
            message: "Congratulations! You are now VIP \(level)!",
            preferredStyle: .alert
        )
        
        alert.addAction(UIAlertAction(title: "OK", style: .default))
        present(alert, animated: true)
    }
}

// MARK: - Array Extension

extension Array {
    subscript(safe index: Index) -> Element? {
        return indices.contains(index) ? self[index] : nil
    }
}
