//
//  LNVIPLevelCarouselView.swift
//  LiveNow
//
//  Created by AI on 2025/8/24.
//

import UIKit
import SnapKit

/// VIP等级轮播视图 - 包含6个VIP等级的轮播展示
class LNVIPLevelCarouselView: UIView {
    
    // MARK: - Properties
    
    /// 轮播数据源
    private var vipLevels: [LNVIPLevelCarouselCell.VIPLevelData] = []
    
    /// 当前选中的索引
    private var currentIndex: Int = 0 {
        didSet {
            pageControl.currentPage = currentIndex
            onPageChanged?(currentIndex)
        }
    }
    
    /// 页面变化回调
    var onPageChanged: ((Int) -> Void)?
    
    /// 轮播项点击回调
    var onItemTapped: ((Int) -> Void)?
    
    // MARK: - UI Elements
    
    /// 轮播集合视图
    private lazy var collectionView: UICollectionView = {
        let layout = UICollectionViewFlowLayout()
        layout.scrollDirection = .horizontal
        layout.minimumLineSpacing = 0
        layout.minimumInteritemSpacing = 0
        
        let collectionView = UICollectionView(frame: .zero, collectionViewLayout: layout)
        collectionView.backgroundColor = .clear
        collectionView.showsHorizontalScrollIndicator = false
        collectionView.isPagingEnabled = true
        collectionView.delegate = self
        collectionView.dataSource = self
        
        // 注册Cell
        collectionView.register(LNVIPLevelCarouselCell.self, 
                              forCellWithReuseIdentifier: "LNVIPLevelCarouselCell")
        
        return collectionView
    }()
    
    /// 页面指示器
    private lazy var pageControl: UIPageControl = {
        let pageControl = UIPageControl()
        pageControl.numberOfPages = 7 // v0-v6 共7个等级
        pageControl.currentPage = 0
        pageControl.pageIndicatorTintColor = UIColor.white.withAlphaComponent(0.3)
        pageControl.currentPageIndicatorTintColor = .white
        pageControl.hidesForSinglePage = false
        pageControl.isUserInteractionEnabled = true
        pageControl.addTarget(self, action: #selector(pageControlValueChanged(_:)), for: .valueChanged)
        return pageControl
    }()
    
    // MARK: - Initialization
    
    override init(frame: CGRect) {
        super.init(frame: frame)
        setupData()
        setupUI()
        setupConstraints()
    }
    
    required init?(coder: NSCoder) {
        super.init(coder: coder)
        setupData()
        setupUI()
        setupConstraints()
    }
    
    // MARK: - Setup Methods
    
    private func setupData() {
        // 创建v0-v6的VIP等级数据
        vipLevels = (0...6).map { level in
            LNVIPLevelCarouselCell.VIPLevelData(level: level)
        }
    }
    
    private func setupUI() {
        backgroundColor = .clear
        
        addSubview(collectionView)
        addSubview(pageControl)
        
        // 设置圆角
        layer.cornerRadius = s(14)
        clipsToBounds = true
    }
    
    private func setupConstraints() {
        // 集合视图约束
        collectionView.snp.makeConstraints { make in
            make.top.left.right.equalToSuperview()
            make.bottom.equalTo(pageControl.snp.top).offset(-s(8))
        }
        
        // 页面指示器约束
        pageControl.snp.makeConstraints { make in
            make.centerX.equalToSuperview()
            make.bottom.equalToSuperview().offset(-s(12))
            make.height.equalTo(s(20))
        }
    }
    
    // MARK: - Public Methods
    
    /// 设置当前VIP等级
    /// - Parameter level: VIP等级 (0-6)
    func setCurrentLevel(_ level: Int) {
        guard level >= 0 && level < vipLevels.count else { return }
        
        currentIndex = level
        
        // 滚动到指定位置
        let indexPath = IndexPath(item: level, section: 0)
        collectionView.scrollToItem(at: indexPath, at: .centeredHorizontally, animated: true)
    }
    
    /// 获取当前选中的VIP等级
    func getCurrentLevel() -> Int {
        return currentIndex
    }
    
    // MARK: - Actions
    
    @objc private func pageControlValueChanged(_ sender: UIPageControl) {
        let targetIndex = sender.currentPage
        setCurrentLevel(targetIndex)
    }
}

// MARK: - UICollectionViewDataSource

extension LNVIPLevelCarouselView: UICollectionViewDataSource {
    
    func collectionView(_ collectionView: UICollectionView, numberOfItemsInSection section: Int) -> Int {
        return vipLevels.count
    }
    
    func collectionView(_ collectionView: UICollectionView, cellForItemAt indexPath: IndexPath) -> UICollectionViewCell {
        let cell = collectionView.dequeueReusableCell(withReuseIdentifier: "LNVIPLevelCarouselCell", 
                                                     for: indexPath) as! LNVIPLevelCarouselCell
        
        let vipData = vipLevels[indexPath.item]
        
        // 模拟进度数据
        let progress: Float = indexPath.item == currentIndex ? 0.7 : 0.3
        let progressText = indexPath.item == currentIndex ? "70% to next level" : "Upgrade to unlock"
        
        cell.configure(with: vipData, progress: progress, progressText: progressText)
        
        return cell
    }
}

// MARK: - UICollectionViewDelegate

extension LNVIPLevelCarouselView: UICollectionViewDelegate {
    
    func collectionView(_ collectionView: UICollectionView, didSelectItemAt indexPath: IndexPath) {
        onItemTapped?(indexPath.item)
    }
}

// MARK: - UICollectionViewDelegateFlowLayout

extension LNVIPLevelCarouselView: UICollectionViewDelegateFlowLayout {
    
    func collectionView(_ collectionView: UICollectionView, layout collectionViewLayout: UICollectionViewLayout, sizeForItemAt indexPath: IndexPath) -> CGSize {
        return collectionView.bounds.size
    }
}

// MARK: - UIScrollViewDelegate

extension LNVIPLevelCarouselView: UIScrollViewDelegate {
    
    func scrollViewDidEndDecelerating(_ scrollView: UIScrollView) {
        let pageWidth = scrollView.frame.width
        let currentPage = Int(scrollView.contentOffset.x / pageWidth)
        
        if currentPage != currentIndex {
            currentIndex = currentPage
        }
    }
    
    func scrollViewDidEndScrollingAnimation(_ scrollView: UIScrollView) {
        let pageWidth = scrollView.frame.width
        let currentPage = Int(scrollView.contentOffset.x / pageWidth)
        
        if currentPage != currentIndex {
            currentIndex = currentPage
        }
    }
}
