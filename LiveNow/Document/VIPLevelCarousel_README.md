# VIP等级轮播组件使用说明

## 概述

VIP等级轮播组件是一个用于展示VIP等级的轮播视图，包含v0-v6共7个等级，每个等级使用对应的背景图片（ic_vip_v0到ic_vip_v6）。

## 组件结构

### 1. LNVIPLevelCarouselCell
- **功能**: 轮播项的Cell类，用于显示单个VIP等级
- **特性**: 
  - 支持背景图片显示
  - 包含等级标题和副标题
  - 显示等级进度条
  - 根据等级自动调整文本颜色

### 2. LNVIPLevelCarouselView
- **功能**: 主要的轮播视图组件
- **特性**:
  - 水平滚动轮播
  - 分页指示器
  - 支持手势滑动和程序控制
  - 页面变化和点击事件回调

### 3. LNVIPLevelCarouselDemo
- **功能**: 演示控制器，展示轮播组件的使用方法
- **特性**:
  - 完整的使用示例
  - 控制按钮测试功能
  - 事件处理演示

## 使用方法

### 基本使用

```swift
// 1. 创建轮播视图
let carouselView = LNVIPLevelCarouselView()

// 2. 添加到父视图
view.addSubview(carouselView)

// 3. 设置约束
carouselView.snp.makeConstraints { make in
    make.top.equalTo(someView.snp.bottom).offset(s(20))
    make.left.right.equalToSuperview().inset(s(20))
    make.height.equalTo(s(200))
}

// 4. 设置回调
carouselView.onPageChanged = { index in
    print("当前VIP等级: \(index)")
}

carouselView.onItemTapped = { index in
    print("点击了VIP等级: \(index)")
}
```

### 程序控制

```swift
// 设置当前VIP等级
carouselView.setCurrentLevel(3) // 跳转到VIP 3

// 获取当前VIP等级
let currentLevel = carouselView.getCurrentLevel()
```

### 自定义配置

```swift
// 在LNVIPLevelCarouselCell中自定义显示内容
cell.configure(
    with: vipData,
    progress: 0.7,
    progressText: "70% to next level"
)
```

## 集成到现有页面

### 在LNProfileViewController中使用

可以替换现有的levelCardView：

```swift
// 替换原有的levelCardView
private lazy var vipCarouselView: LNVIPLevelCarouselView = {
    let view = LNVIPLevelCarouselView()
    view.onPageChanged = { [weak self] level in
        self?.handleVIPLevelChanged(level)
    }
    return view
}()

// 在setupUI中添加
cardsContainerView.addSubview(vipCarouselView)

// 在setupConstraints中设置约束
vipCarouselView.snp.makeConstraints { make in
    make.top.left.right.equalToSuperview()
    make.height.equalTo(s(200))
}

// 根据用户数据设置当前等级
private func updateVIPLevel(_ user: LNUserModel?) {
    guard let user = user else {
        vipCarouselView.setCurrentLevel(0)
        return
    }
    
    let levelKey = user.levelKey
    vipCarouselView.setCurrentLevel(levelKey)
}
```

## 图片资源

组件使用以下图片资源：
- `ic_vip_v0` - VIP 0等级背景
- `ic_vip_v1` - VIP 1等级背景
- `ic_vip_v2` - VIP 2等级背景
- `ic_vip_v3` - VIP 3等级背景
- `ic_vip_v4` - VIP 4等级背景
- `ic_vip_v5` - VIP 5等级背景
- `ic_vip_v6` - VIP 6等级背景

## 测试方法

1. 运行LNVIPLevelCarouselDemo控制器
2. 使用手势滑动测试轮播功能
3. 点击分页指示器测试跳转功能
4. 使用控制按钮测试程序控制功能

## 注意事项

1. 确保所有VIP等级图片资源已正确添加到项目中
2. 轮播视图需要设置固定高度约束
3. 回调函数中避免强引用循环，使用weak self
4. 根据实际需求调整进度显示逻辑

## 扩展功能

可以根据需要扩展以下功能：
- 自动轮播定时器
- 更多的动画效果
- 自定义分页指示器样式
- 支持垂直滚动
- 添加音效和触觉反馈
