//
//  LNMyVipLevelController.swift
//  LiveNow
//
//  Created by Augment Agent on 2025/08/15.
//

import UIKit
import SnapKit
import FSPagerView

/// VIP等级页面
/// 显示用户VIP等级信息、轮播图、权益列表等
class LNMyVipLevelController: LNBaseController {

    // 使用透明导航栏
    override var navigationSolidColor: UIColor { return .clear }
    override var navigationTitleColor: UIColor { return .white }

    // MARK: - Data Models
    struct VipLevelInfo {
        let level: Int
        let title: String
        let description: String
        let icon: String
        let progress: Int // 到下一级的进度
        let nextLevelRequirement: Int
    }

    struct VipPrivilege {
        let title: String
        let description: String
        let icon: String
    }

    // MARK: - Properties
    private var currentVipInfo = VipLevelInfo(
        level: 0,
        title: "V0",
        description: "100 to the next level",
        icon: "ic_vip_v0",
        progress: 100,
        nextLevelRequirement: 100
    )

    private let vipPrivileges = [
        VipPrivilege(title: "VIP 权益一", description: "Free times to send messages", icon: "ic_vip_star"),
        VipPrivilege(title: "VIP 权益二", description: "Free times to send messages", icon: "ic_vip_star"),
        VipPrivilege(title: "VIP 权益三", description: "Free times to send messages", icon: "ic_vip_star"),
        VipPrivilege(title: "VIP 权益四", description: "Free times to send messages", icon: "ic_vip_star"),
        VipPrivilege(title: "VIP 权益五", description: "Free times to send messages", icon: "ic_vip_star"),
        VipPrivilege(title: "VIP 权益六", description: "Free times to send messages", icon: "ic_vip_star")
    ]

    // MARK: - UI Elements
    private lazy var backgroundImageView: UIImageView = {
        let imageView = UIImageView()
        imageView.image = UIImage(named: "ic_vip_bg")
        imageView.contentMode = .scaleAspectFill
        imageView.clipsToBounds = true
        return imageView
    }()

    private lazy var scrollView: UIScrollView = {
        let scrollView = UIScrollView()
        scrollView.showsVerticalScrollIndicator = false
        scrollView.backgroundColor = .clear
        return scrollView
    }()

    private lazy var contentView: UIView = {
        let view = UIView()
        view.backgroundColor = .clear
        return view
    }()

    // 顶部轮播
    private lazy var pagerView: FSPagerView = {
        let pager = FSPagerView()
        pager.dataSource = self
        pager.delegate = self
        pager.register(FSPagerViewCell.self, forCellWithReuseIdentifier: "cell")
        pager.itemSize = CGSize(width: s(300), height: s(160))
        pager.interitemSpacing = s(20)
        pager.isInfinite = true
        pager.automaticSlidingInterval = 3.0
        pager.backgroundColor = .clear
        return pager
    }()

    // 中间渐变按钮
    private lazy var privilegeButton: UIButton = {
        let button = UIButton(type: .custom)
        button.setTitle("LV 0-5 Privilege", for: .normal)
        button.setTitleColor(.white, for: .normal)
        button.titleLabel?.font = LNFont.medium(18)
        button.layer.cornerRadius = s(25)
        button.clipsToBounds = true
        return button
    }()

    private lazy var privilegeButtonGradientLayer: CAGradientLayer = {
        let layer = CAGradientLayer()
        layer.colors = [
            UIColor.hex(hexString: "#F4F46D").cgColor,
            UIColor.hex(hexString: "#26FFDB").cgColor
        ]
        layer.startPoint = CGPoint(x: 0, y: 0.5)
        layer.endPoint = CGPoint(x: 1, y: 0.5)
        return layer
    }()

    // 权益列表容器
    private lazy var privilegesContainer: UIView = {
        let view = UIView()
        view.backgroundColor = UIColor.white.withAlphaComponent(0.15)
        view.layer.cornerRadius = s(20)
        view.clipsToBounds = true
        return view
    }()

    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        setupConstraints()
        setupGradientButton()
        createPrivilegeViews()
        setupNavigationBar()
        setupInitialVipLevel()
    }

    private func setupNavigationBar() {
        // 设置返回按钮为白色
        if let nav = navigationController, nav.viewControllers.count > 1 {
            let backButton = UIBarButtonItem(
                image: UIImage(systemName: "chevron.left"),
                style: .plain,
                target: self,
                action: #selector(onBackButtonItem)
            )
            backButton.tintColor = .white
            navigationItem.leftBarButtonItem = backButton
        }
    }

    /// 设置初始VIP等级显示
    private func setupInitialVipLevel() {
        let currentLevel = getCurrentUserLevel()

        // 更新特权按钮文本
        updatePrivilegeButtonTitle(for: currentLevel)

        // 延迟执行，确保pagerView已经完成布局
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) { [weak self] in
            self?.pagerView.scrollToItem(at: currentLevel, animated: false)
        }
    }

    /// 更新特权按钮标题
    private func updatePrivilegeButtonTitle(for level: Int) {
        if level == 0 {
            privilegeButton.setTitle("LV 0 Basic Privileges", for: .normal)
        } else if level == 6 {
            privilegeButton.setTitle("LV 6 Ultimate Privileges", for: .normal)
        } else {
            privilegeButton.setTitle("LV \(level) Premium Privileges", for: .normal)
        }
    }

    override func viewDidLayoutSubviews() {
        super.viewDidLayoutSubviews()
        privilegeButtonGradientLayer.frame = privilegeButton.bounds
    }

    // MARK: - Setup Methods
    private func setupUI() {
        title = "Level"
        view.backgroundColor = .clear

        view.addSubview(backgroundImageView)
        view.addSubview(scrollView)
        scrollView.addSubview(contentView)

        contentView.addSubview(pagerView)
        contentView.addSubview(privilegeButton)
        contentView.addSubview(privilegesContainer)
    }

    private func setupConstraints() {
        backgroundImageView.snp.makeConstraints { make in
            make.left.right.equalToSuperview()
            make.top.equalTo(-knavH)
            make.bottom.equalToSuperview()
        }

        scrollView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }

        contentView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
            make.width.equalToSuperview()
        }

        pagerView.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(s(20))
            make.left.right.equalToSuperview()
            make.height.equalTo(s(180))
        }

        privilegeButton.snp.makeConstraints { make in
            make.top.equalTo(pagerView.snp.bottom).offset(s(30))
            make.left.equalToSuperview().offset(s(20))
            make.right.equalToSuperview().offset(s(-20))
            make.height.equalTo(s(50))
        }

        privilegesContainer.snp.makeConstraints { make in
            make.top.equalTo(privilegeButton.snp.bottom).offset(s(30))
            make.left.equalToSuperview().offset(s(20))
            make.right.equalToSuperview().offset(s(-20))
            make.bottom.equalToSuperview().offset(s(-20))
        }
    }

    private func setupGradientButton() {
        privilegeButton.layer.insertSublayer(privilegeButtonGradientLayer, at: 0)
    }

    private func createPrivilegeViews() {
        var previousView: UIView?

        for (index, privilege) in vipPrivileges.enumerated() {
            let privilegeView = createPrivilegeItemView(privilege: privilege)
            privilegesContainer.addSubview(privilegeView)

            privilegeView.snp.makeConstraints { make in
                make.left.equalToSuperview().offset(s(20))
                make.right.equalToSuperview().offset(s(-20))
                make.height.equalTo(s(60))

                if let previous = previousView {
                    make.top.equalTo(previous.snp.bottom).offset(s(15))
                } else {
                    make.top.equalToSuperview().offset(s(20))
                }

                if index == vipPrivileges.count - 1 {
                    make.bottom.equalToSuperview().offset(s(-20))
                }
            }

            previousView = privilegeView
        }
    }

    /// 获取当前用户VIP等级
    private func getCurrentUserLevel() -> Int {
        if let user = LNUserManager.shared.userModel {
            return user.levelKey
        }
        return 0 // 默认等级0
    }

    private func createPrivilegeItemView(privilege: VipPrivilege) -> UIView {
        let containerView = UIView()
        containerView.backgroundColor = .clear

        let iconImageView = UIImageView()
        iconImageView.image = UIImage(named: privilege.icon)
        iconImageView.contentMode = .scaleAspectFit
        iconImageView.tintColor = UIColor.hex(hexString: "#26FFDB")

        let titleLabel = UILabel()
        titleLabel.text = privilege.title
        titleLabel.font = LNFont.medium(16)
        titleLabel.textColor = .white

        let descriptionLabel = UILabel()
        descriptionLabel.text = privilege.description
        descriptionLabel.font = LNFont.regular(14)
        descriptionLabel.textColor = UIColor.white.withAlphaComponent(0.8)

        containerView.addSubview(iconImageView)
        containerView.addSubview(titleLabel)
        containerView.addSubview(descriptionLabel)

        iconImageView.snp.makeConstraints { make in
            make.left.equalToSuperview()
            make.centerY.equalToSuperview()
            make.width.height.equalTo(s(24))
        }

        titleLabel.snp.makeConstraints { make in
            make.left.equalTo(iconImageView.snp.right).offset(s(15))
            make.top.equalToSuperview().offset(s(8))
            make.right.equalToSuperview()
        }

        descriptionLabel.snp.makeConstraints { make in
            make.left.equalTo(titleLabel)
            make.top.equalTo(titleLabel.snp.bottom).offset(s(4))
            make.right.equalToSuperview()
            make.bottom.equalToSuperview().offset(s(-8))
        }

        return containerView
    }
}

// MARK: - FSPagerView DataSource & Delegate
extension LNMyVipLevelController: FSPagerViewDataSource, FSPagerViewDelegate {

    func numberOfItems(in pagerView: FSPagerView) -> Int {
        return 7 // 显示v0-v6共7个轮播项
    }

    func pagerView(_ pagerView: FSPagerView, cellForItemAt index: Int) -> FSPagerViewCell {
        let cell = pagerView.dequeueReusableCell(withReuseIdentifier: "cell", at: index)

        // 清除之前的子视图
        cell.contentView.subviews.forEach { $0.removeFromSuperview() }

        // 设置Cell基本样式
        cell.contentView.backgroundColor = .clear
        cell.contentView.layer.cornerRadius = s(14)
        cell.contentView.clipsToBounds = true

        // 创建背景图片视图 - 参考LNProfileViewController的levelCardBackgroundImageView
        let backgroundImageView = UIImageView()
        backgroundImageView.image = UIImage(named: "ic_vip_v\(index)") ?? UIImage(named: "ic_vip_v0")
        backgroundImageView.contentMode = .scaleAspectFill
        backgroundImageView.clipsToBounds = true

        // 创建等级标签 - 参考LNProfileViewController的设计
        let levelLabel = UILabel()
        levelLabel.text = "VIP \(index)"
        levelLabel.font = LNFont.bold(18)
        levelLabel.textColor = index == 0 ? UIColor(hexString: "#6D75AD") : .white
        levelLabel.textAlignment = .left

        // 创建进度文本标签 - 参考LNProfileViewController的nextLevelLabel
        let progressLabel = UILabel()
        if index == 6 {
            progressLabel.text = "Max level reached"
        } else {
            let nextLevelPoints = (index + 1) * 100
            progressLabel.text = "\(nextLevelPoints) to the next level"
        }
        progressLabel.font = LNFont.regular(12)
        progressLabel.textColor = index == 0 ? UIColor(hexString: "#6D75AD") : UIColor.white.withAlphaComponent(0.9)
        progressLabel.textAlignment = .left

        // 创建进度条 - 参考LNProfileViewController的levelProgressView
        let progressView = UIProgressView(progressViewStyle: .default)
        progressView.layer.cornerRadius = s(3)
        progressView.clipsToBounds = true

        // 根据等级设置进度条颜色
        if index == 0 {
            progressView.progressTintColor = UIColor(hexString: "#6D75AD")
            progressView.trackTintColor = UIColor(hexString: "#6D75AD").withAlphaComponent(0.3)
        } else {
            progressView.progressTintColor = .white
            progressView.trackTintColor = UIColor.white.withAlphaComponent(0.3)
        }

        // 设置进度值（模拟当前等级进度）
        let currentUserLevel = getCurrentUserLevel()
        if index == currentUserLevel {
            progressView.progress = 0.7 // 当前等级70%进度
        } else if index < currentUserLevel {
            progressView.progress = 1.0 // 已完成等级
        } else {
            progressView.progress = 0.0 // 未达到等级
        }

        // 添加子视图
        cell.contentView.addSubview(backgroundImageView)
        cell.contentView.addSubview(levelLabel)
        cell.contentView.addSubview(progressLabel)
        cell.contentView.addSubview(progressView)

        // 设置约束 - 参考LNProfileViewController的布局
        backgroundImageView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }

        levelLabel.snp.makeConstraints { make in
            make.left.equalToSuperview().offset(s(16))
            make.top.equalToSuperview().offset(s(16))
            make.right.lessThanOrEqualToSuperview().offset(-s(16))
        }

        progressView.snp.makeConstraints { make in
            make.left.equalToSuperview().offset(s(16))
            make.right.equalToSuperview().offset(-s(16))
            make.height.equalTo(s(6))
            make.bottom.equalToSuperview().offset(-s(6))
        }

        progressLabel.snp.makeConstraints { make in
            make.left.equalToSuperview().offset(s(16))
            make.bottom.equalTo(progressView.snp.top).offset(-s(8))
            make.right.lessThanOrEqualToSuperview().offset(-s(16))
        }

        return cell
    }

    func pagerViewDidScroll(_ pagerView: FSPagerView) {
        // 当轮播滚动时，更新特权按钮文本
        let currentIndex = pagerView.currentIndex
        updatePrivilegeButtonTitle(for: currentIndex)
    }

    func pagerView(_ pagerView: FSPagerView, didSelectItemAt index: Int) {
        // 处理轮播项点击事件
        let currentUserLevel = getCurrentUserLevel()

        if index > currentUserLevel {
            // 点击了更高等级，显示升级提示
            showUpgradeAlert(for: index)
        } else if index == currentUserLevel {
            // 点击了当前等级，显示当前等级详情
            showCurrentLevelInfo(for: index)
        } else {
            // 点击了较低等级，显示历史等级信息
            showLevelHistory(for: index)
        }

        print("Selected VIP level: V\(index)")
    }

    /// 显示升级提示
    private func showUpgradeAlert(for level: Int) {
        let alert = UIAlertController(
            title: "Upgrade to VIP \(level)",
            message: "Would you like to upgrade to VIP \(level) and unlock more privileges?",
            preferredStyle: .alert
        )

        alert.addAction(UIAlertAction(title: "Upgrade", style: .default) { _ in
            // 这里可以跳转到充值页面或会员中心
            let membershipVC = LNMembershipCenterViewController()
            membershipVC.hidesBottomBarWhenPushed = true
            self.navigationController?.pushViewController(membershipVC, animated: true)
        })

        alert.addAction(UIAlertAction(title: "Cancel", style: .cancel))
        present(alert, animated: true)
    }

    /// 显示当前等级信息
    private func showCurrentLevelInfo(for level: Int) {
        let alert = UIAlertController(
            title: "Current VIP \(level)",
            message: "You are currently at VIP \(level). Enjoy your premium privileges!",
            preferredStyle: .alert
        )

        alert.addAction(UIAlertAction(title: "OK", style: .default))
        present(alert, animated: true)
    }

    /// 显示历史等级信息
    private func showLevelHistory(for level: Int) {
        let alert = UIAlertController(
            title: "VIP \(level) History",
            message: "You have already surpassed VIP \(level). Great achievement!",
            preferredStyle: .alert
        )

        alert.addAction(UIAlertAction(title: "OK", style: .default))
        present(alert, animated: true)
    }
}